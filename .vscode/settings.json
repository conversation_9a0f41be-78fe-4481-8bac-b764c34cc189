{
  "workbench.colorTheme": "Default High Contrast Light",
  "editor.fontFamily": "'Wide Latin', 'Comic Sans MS', 'DejaVu Sans Serif', cursive, sans-serif",

  "highlight.regexFlags": "gi",
  "highlight.regexes": {
    "(?= *[functio])( )*(f?u?n?c?t?i?o?n?)+( *)([^()\n ]+)( *\\()(.*?)(\\) +=>)": {
      "decorations": [
        {}, //indentation
        {
          "color": "#ffff46" //function keyword
        },
        {},
        {
          "color": "#46ccff" //function name
        },
        {},
        {
          "color": "#8043f7" //function params
        },
        {}
      ]
    },
    "const (const|var) +(var|(?!\\1)const)": {
      "decorations": [
        {
          "backgroundColor": "#ff4346", // this is to highlight an invalid usage of const const const
          "color": "#1f1f1f"
        },
        {}
      ]
    },
    "(const|var)( +)(const|var)( +)([^ +\\-\\*\\/<>=\\(\\)\\[\\]!;:\\.{}\n]+)(([^ ]+?))?( *)([+\\-\\/*]?)(= *)([^!\n?]+)": {
      "decorations": [
        {
          "color": "#46ff80" // first const
        },
        {},
        {
          "color": "#4680ff" // second const
        },
        {},
        {
          "color": "#ff8046" // variable name
        },
        {
          "color": "#ff80de" //lifetime
        }
      ]
    }
  }
}
