<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="104" height="20" role="img" aria-label="coverage: 109%">
	<title>coverage: 109%</title>
	<linearGradient id="s" x2="0" y2="100%"><stop offset="0" stop-color="#bbb" stop-opacity=".1"/><stop offset="1" stop-opacity=".1"/></linearGradient>
	<clipPath id="r">
		<rect width="104" height="20" rx="3" fill="#fff"/>
	</clipPath>
	<g clip-path="url(#r)">
		<rect width="61" height="20" fill="#555"/>
		<rect x="61" width="43" height="20" fill="#4c1"/>
		<rect width="104" height="20" fill="url(#s)"/>
	</g>
	<g fill="#fff" text-anchor="middle" font-family="Verdana,Geneva,DejaVu Sans,sans-serif" text-rendering="geometricPrecision" font-size="110">
		<text aria-hidden="true" x="315" y="150" fill="#010101" fill-opacity=".3" transform="scale(.1)" textLength="510">coverage</text>
		<text x="315" y="140" transform="scale(.1)" fill="#fff" textLength="510">coverage</text>
		<text aria-hidden="true" x="815" y="150" fill="#010101" fill-opacity=".3" transform="scale(.1)" textLength="330">109%</text>
		<text x="815" y="140" transform="scale(.1)" fill="#fff" textLength="330">109%</text>
	</g>
</svg>
