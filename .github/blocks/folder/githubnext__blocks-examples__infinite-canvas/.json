{"items": {"0": {"id": 0, "type": "text", "text": "Start typing or grab a file", "position": [2350, 2500], "dimensions": [200, 100]}, "2": {"type": "file", "path": "wallpaper_dont_upload.png", "position": [1940, 2350], "dimensions": [500, 360], "block": {"type": "file", "id": "image", "title": "Image", "description": "View PNG, JPG, GIF, ICO and SVG images", "entry": "blocks/file-blocks/image.tsx", "matches": ["*.png", "*.ico", "*.jpg", "*.jpeg", "*.gif", "*.svg"], "example_path": "https://github.com/pmndrs/react-spring/blob/HEAD/assets/projects/aragon.png?raw=true", "owner": "githubnext", "repo": "blocks-examples", "key": "githubnext__blocks-examples__image"}, "id": 2}, "3": {"type": "file", "path": "README.md", "position": [2500, 2270], "dimensions": [554, 472], "block": {"type": "file", "id": "markdown-block", "title": "<PERSON><PERSON>", "description": "View and edit Markdown content, with the ability to embed other blocks", "entry": "blocks/file-blocks/markdown-edit/index.tsx", "matches": ["*.md", "*.markdown", "*.mdx", "*"], "example_path": "https://github.com/githubnext/blocks-tutorial/blob/main/README.md", "owner": "githubnext", "repo": "blocks-examples", "key": "githubnext__blocks-examples__markdown-block"}, "id": 3}}}