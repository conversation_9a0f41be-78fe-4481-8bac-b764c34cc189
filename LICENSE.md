# Examples

Congratulations! You found the hidden examples page!

Here are some examples of Gulf of Mexico in action!

## Hello world

```java
Hello world?
```

## FizzBuzz

```java
const var i: Int!

when (i % 3 = 0 && i % 5 = 0) "FizzBuzz"?
else when (i % 3 = 0) "Fizz"?
else when (i % 5 = 0) "Buzz"?
else i?

when (i < 20) i++!
i = 0!
```

## <PERSON><PERSON><PERSON><PERSON>

```java
fi bonacci (n) => {
   const var sum = 1!
   const var i = 0!
   when (i < n) {
      sum += sum + previous sum!
      i++!
   }
}

when (i < 10) {
   bonacci(i)?
   i++!
}
```

## Counter

```java
const var count = 0!
var const Button = <button></button>!
when count Button.textContent = Count: £{count}!
Button.addEventListener click => count++!
document.body.append Button!
```

## The Billion Dollar Mistake

```java
delete null!
```

## Autocomplete Example
```java
c
```
